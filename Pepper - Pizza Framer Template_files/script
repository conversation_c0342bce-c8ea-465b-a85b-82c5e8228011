"use strict";(()=>{var pe,V,he;function Re(){pe=Intl.DateTimeFormat().resolvedOptions(),V=pe.timeZone,he=pe.locale}var Te=null;function b(){if(!Te){let t=document.currentScript;Te={src:t.src,framerSiteId:t?t.getAttribute("data-fid"):null,trackNavigation:!(t!=null&&t.hasAttribute("data-no-nt")),cdn:"https://framerusercontent.com/sites/"}}return Te}var mt=b();function x(){return Math.floor((1+Math.random())*65536).toString(16).substring(1)}function gt(){return`${x()}${x()}-${x()}-${x()}-${x()}-${x()}${x()}${x()}`}var E=class{constructor(e,o){this.timestamp=Date.now(),this.data={type:"track",uuid:gt(),event:e,...o,context:{framerSiteId:mt.framerSiteId,visitTimeOrigin:performance.timeOrigin,origin:location.origin,pathname:location.pathname,search:location.search,...o.context}}}serialize(e){return{source:"framer.site",timestamp:this.timestamp,sentTimestamp:e,data:this.data}}};var Ie=new Set,ve=t=>Ie.forEach(({callback:e,on:o})=>o===t&&e()),R=(t,e="lazy")=>Ie.add({callback:t,on:e});addEventListener("visibilitychange",()=>{document.hidden&&ve("lazy")},!0);addEventListener("pagehide",()=>ve("lazy"),!0);addEventListener("load",()=>ve("load"));var G,te,N;function ft(t,e,o,r){let i=JSON.stringify(e);try{return window.fetchLater(t,{method:"POST",body:i,signal:r,activateAfter:o?3e3:10*6e4})}catch{return _e(t,i),{activated:!0}}}function pt(t,e,o){return!1}function _e(t,e){fetch(t,{method:"POST",body:e})}function ht(t,e){return navigator.sendBeacon(t,e)}function ye(t,e,o){if(!pt(t,e,o)){let r=JSON.stringify(e);ht(t,r)||_e(t,r)}}var ne=new Set;function Oe(){for(let t of ne)t();ne.clear()}var I=window.scheduler,Tt=I&&"yield"in I,Be=I&&"postTask"in I;function _(t=!1){return new Promise(e=>{if(ne.add(e),!document.hidden){requestAnimationFrame(()=>{let o=()=>{ne.delete(e),e()};t?Tt?I.yield().then(o):Be?I.postTask(o):o():Be?I.postTask(o,{priority:"background"}):setTimeout(o,1)});return}Oe()})}R(Oe,"lazy");var vt=b(),yt=new URL(vt.src),Ne=`${yt.origin}/anonymous`;function We(t,e){return t==="eager"||e==="eager"?"eager":e??t}var re=new Set,Se=!1;function Ee(){if(re.size===0)return;if(!Se){Se=!0,queueMicrotask(Ee);return}let t=[],e=Date.now();re.forEach(o=>o.forEach(r=>t.push(r.serialize(e)))),re.clear(),ye(Ne,t,!1),Se=!1}async function w(t,e="lazy"){if(location.protocol.startsWith("https")&&t.length!==0){if(e==="eager"){await _();let o=Date.now();ye(Ne,t.map(r=>r.serialize(o)),!0);return}re.add(t),document.hidden&&Ee()}}R(Ee,"lazy");var W="__framer_events";function Ue(){window[W]||(window[W]=[]);function t(e){let o,r=e.map(i=>{let[a,n,s]=i;return o=We(o,s),new E(a,n)});w(r,o??"eager")}window[W].length>0&&(t(window[W]),window[W].length=0),window[W].push=(...e)=>(t(e),-1)}var St=b();function Z(t){let e=[new E("published_site_pageview",{referrer:(t==null?void 0:t.initialReferrer)||null,url:location.href,hostname:location.hostname||null,pathname:location.pathname||null,hash:location.hash||null,search:location.search||null,framerSiteId:St.framerSiteId,timezone:V,locale:he})];w(e,"eager")}function $e(){addEventListener("popstate",()=>Z());let t=history.pushState;history.pushState=(...e)=>{t.apply(history,e),Z()}}var oe=class{t;o=0;i=[];u(e){var i;if(e.hadRecentInput)return;let o=this.i[0],r=this.i.at(-1);this.o&&o&&r&&e.startTime-r.startTime<1e3&&e.startTime-o.startTime<5e3?(this.o+=e.value,this.i.push(e)):(this.o=e.value,this.i=[e]),(i=this.t)==null||i.call(this,e)}},q=()=>{let t=performance.getEntriesByType("navigation")[0];if(t&&t.responseStart>0&&t.responseStart<performance.now())return t},ae=t=>{if(document.readyState==="loading")return"loading";{let e=q();if(e){if(t<e.domInteractive)return"loading";if(e.domContentLoadedEventStart===0||t<e.domContentLoadedEventStart)return"dom-interactive";if(e.domComplete===0||t<e.domComplete)return"dom-content-loaded"}}return"complete"},Et=t=>{let e=t.nodeName;return t.nodeType===1?e.toLowerCase():e.toUpperCase().replace(/^#/,"")},we=t=>{let e="";try{for(;(t==null?void 0:t.nodeType)!==9;){let o=t,r=o.id?"#"+o.id:[Et(o),...Array.from(o.classList).sort()].join(".");if(e.length+r.length>99)return e||r;if(e=e?r+">"+e:r,o.id)break;t=o.parentNode}}catch{}return e},be=new WeakMap;function $(t,e){return be.get(t)||be.set(t,new e),be.get(t)}var Ke=-1,et=()=>Ke,j=t=>{addEventListener("pageshow",e=>{e.persisted&&(Ke=e.timeStamp,t(e))},!0)},L=(t,e,o,r)=>{let i,a;return n=>{e.value>=0&&(n||r)&&(a=e.value-(i??0),(a||i===void 0)&&(i=e.value,e.delta=a,e.rating=((s,l)=>s>l[1]?"poor":s>l[0]?"needs-improvement":"good")(e.value,o),t(e)))}},De=t=>{requestAnimationFrame(()=>requestAnimationFrame(()=>t()))},X=()=>{let t=q();return(t==null?void 0:t.activationStart)??0},P=(t,e=-1)=>{let o=q(),r="navigate";return et()>=0?r="back-forward-cache":o&&(document.prerendering||X()>0?r="prerender":document.wasDiscarded?r="restore":o.type&&(r=o.type.replace(/_/g,"-"))),{name:t,value:e,rating:"good",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},H=(t,e,o={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){let r=new PerformanceObserver(i=>{Promise.resolve().then(()=>{e(i.getEntries())})});return r.observe({type:t,buffered:!0,...o}),r}}catch{}},Me=t=>{let e=!1;return()=>{e||(t(),e=!0)}},U=-1,He=()=>document.visibilityState!=="hidden"||document.prerendering?1/0:0,se=t=>{document.visibilityState==="hidden"&&U>-1&&(U=t.type==="visibilitychange"?t.timeStamp:0,bt())},qe=()=>{addEventListener("visibilitychange",se,!0),addEventListener("prerenderingchange",se,!0)},bt=()=>{removeEventListener("visibilitychange",se,!0),removeEventListener("prerenderingchange",se,!0)},tt=()=>{var t;if(U<0){let e=X();U=(document.prerendering||(t=globalThis.performance.getEntriesByType("visibility-state").filter(r=>r.name==="hidden"&&r.startTime>e)[0])==null?void 0:t.startTime)??He(),qe(),j(()=>{setTimeout(()=>{U=He(),qe()})})}return{get firstHiddenTime(){return U}}},le=t=>{document.prerendering?addEventListener("prerenderingchange",()=>t(),!0):t()},je=[1800,3e3],nt=(t,e={})=>{le(()=>{let o=tt(),r,i=P("FCP"),a=H("paint",n=>{for(let s of n)s.name==="first-contentful-paint"&&(a.disconnect(),s.startTime<o.firstHiddenTime&&(i.value=Math.max(s.startTime-X(),0),i.entries.push(s),r(!0)))});a&&(r=L(t,i,je,e.reportAllChanges),j(n=>{i=P("FCP"),r=L(t,i,je,e.reportAllChanges),De(()=>{i.value=performance.now()-n.timeStamp,r(!0)})}))})},Je=[.1,.25],Ve=t=>t.find(e=>{var o;return((o=e.node)==null?void 0:o.nodeType)===1})||t[0],rt=(t,e={})=>{let o=$(e=Object.assign({},e),oe),r=new WeakMap;o.t=i=>{var a;if((a=i==null?void 0:i.sources)!=null&&a.length){let n=Ve(i.sources);if(n){let s=(e.generateTarget??we)(n.node);r.set(n,s)}}},((i,a={})=>{nt(Me(()=>{let n,s=P("CLS",0),l=$(a,oe),u=c=>{for(let p of c)l.u(p);l.o>s.value&&(s.value=l.o,s.entries=l.i,n())},m=H("layout-shift",u);m&&(n=L(i,s,Je,a.reportAllChanges),document.addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&(u(m.takeRecords()),n(!0))}),j(()=>{l.o=0,s=P("CLS",0),n=L(i,s,Je,a.reportAllChanges),De(()=>n())}),setTimeout(n))}))})(i=>{let a=(n=>{var l;let s={};if(n.entries.length){let u=n.entries.reduce((m,c)=>m.value>c.value?m:c);if((l=u==null?void 0:u.sources)!=null&&l.length){let m=Ve(u.sources);m&&(s={largestShiftTarget:r.get(m),largestShiftTime:u.startTime,largestShiftValue:u.value,largestShiftSource:m,largestShiftEntry:u,loadState:ae(u.startTime)})}}return Object.assign(n,{attribution:s})})(i);t(a)},e)},it=(t,e={})=>{nt(o=>{let r=(i=>{let a={timeToFirstByte:0,firstByteToFCP:i.value,loadState:ae(et())};if(i.entries.length){let n=q(),s=i.entries.at(-1);if(n){let l=n.activationStart||0,u=Math.max(0,n.responseStart-l);a={timeToFirstByte:u,firstByteToFCP:i.value-u,loadState:ae(i.entries[0].startTime),navigationEntry:n,fcpEntry:s}}}return Object.assign(i,{attribution:a})})(o);t(r)},e)},ot=0,Ce=1/0,ie=0,Ct=t=>{for(let e of t)e.interactionId&&(Ce=Math.min(Ce,e.interactionId),ie=Math.max(ie,e.interactionId),ot=ie?(ie-Ce)/7+1:0)},Le,Ge=()=>Le?ot:performance.interactionCount??0,Lt=()=>{"interactionCount"in performance||Le||(Le=H("event",Ct,{type:"event",buffered:!0,durationThreshold:0}))},Qe=0,ce=class{l=[];h=new Map;m;p;v(){Qe=Ge(),this.l.length=0,this.h.clear()}M(){let e=Math.min(this.l.length-1,Math.floor((Ge()-Qe)/50));return this.l[e]}u(e){var i,a;if((i=this.m)==null||i.call(this,e),!e.interactionId&&e.entryType!=="first-input")return;let o=this.l.at(-1),r=this.h.get(e.interactionId);if(r||this.l.length<10||e.duration>o.T){if(r?e.duration>r.T?(r.entries=[e],r.T=e.duration):e.duration===r.T&&e.startTime===r.entries[0].startTime&&r.entries.push(e):(r={id:e.interactionId,entries:[e],T:e.duration},this.h.set(r.id,r),this.l.push(r)),this.l.sort((n,s)=>s.T-n.T),this.l.length>10){let n=this.l.splice(10);for(let s of n)this.h.delete(s.id)}(a=this.p)==null||a.call(this,r)}}},Pe=t=>{let e=globalThis.requestIdleCallback||setTimeout;document.visibilityState==="hidden"?t():(t=Me(t),document.addEventListener("visibilitychange",t,{once:!0}),e(()=>{t(),document.removeEventListener("visibilitychange",t)}))},Ze=[200,500],at=(t,e={})=>{let o=$(e=Object.assign({},e),ce),r=[],i=[],a=0,n=new WeakMap,s=new WeakMap,l=!1,u=()=>{l||(Pe(m),l=!0)},m=()=>{let d=o.l.map(T=>n.get(T.entries[0])),g=i.length-50;i=i.filter((T,y)=>y>=g||d.includes(T));let f=new Set;for(let T of i){let y=c(T.startTime,T.processingEnd);for(let S of y)f.add(S)}let h=r.length-1-50;r=r.filter((T,y)=>T.startTime>a&&y>h||f.has(T)),l=!1};o.m=d=>{let g=d.startTime+d.duration,f;a=Math.max(a,d.processingEnd);for(let h=i.length-1;h>=0;h--){let T=i[h];if(Math.abs(g-T.renderTime)<=8){f=T,f.startTime=Math.min(d.startTime,f.startTime),f.processingStart=Math.min(d.processingStart,f.processingStart),f.processingEnd=Math.max(d.processingEnd,f.processingEnd),f.entries.push(d);break}}f||(f={startTime:d.startTime,processingStart:d.processingStart,processingEnd:d.processingEnd,renderTime:g,entries:[d]},i.push(f)),(d.interactionId||d.entryType==="first-input")&&n.set(d,f),u()},o.p=d=>{if(!s.get(d)){let g=(e.generateTarget??we)(d.entries[0].target);s.set(d,g)}};let c=(d,g)=>{let f=[];for(let h of r)if(!(h.startTime+h.duration<d)){if(h.startTime>g)break;f.push(h)}return f},p=d=>{let g=d.entries[0],f=n.get(g),h=g.processingStart,T=Math.max(g.startTime+g.duration,h),y=Math.min(f.processingEnd,T),S=f.entries.sort((v,C)=>v.processingStart-C.processingStart),A=c(g.startTime,y),D=o.h.get(g.interactionId),F={interactionTarget:s.get(D),interactionType:g.name.startsWith("key")?"keyboard":"pointer",interactionTime:g.startTime,nextPaintTime:T,processedEventEntries:S,longAnimationFrameEntries:A,inputDelay:h-g.startTime,processingDuration:y-h,presentationDelay:T-y,loadState:ae(g.startTime),longestScript:void 0,totalScriptDuration:void 0,totalStyleAndLayoutDuration:void 0,totalPaintDuration:void 0,totalUnattributedDuration:void 0};return(v=>{var Ae;if(!((Ae=v.longAnimationFrameEntries)!=null&&Ae.length))return;let C=v.interactionTime,z=v.inputDelay,Y=v.processingDuration,M,O,ue=0,J=0,me=0,ge=0;for(let K of v.longAnimationFrameEntries){J=J+K.startTime+K.duration-K.styleAndLayoutStart;for(let k of K.scripts){let Fe=k.startTime+k.duration;if(Fe<C)continue;let ee=Fe-Math.max(C,k.startTime),ze=k.duration?ee/k.duration*k.forcedStyleAndLayoutDuration:0;ue+=ee-ze,J+=ze,ee>ge&&(O=k.startTime<C+z?"input-delay":k.startTime>=C+z+Y?"presentation-delay":"processing-duration",M=k,ge=ee)}}let fe=v.longAnimationFrameEntries.at(-1),xe=fe?fe.startTime+fe.duration:0;xe>=C+z+Y&&(me=v.nextPaintTime-xe),M&&O&&(v.longestScript={entry:M,subpart:O,intersectingDuration:ge}),v.totalScriptDuration=ue,v.totalStyleAndLayoutDuration=J,v.totalPaintDuration=me,v.totalUnattributedDuration=v.nextPaintTime-C-ue-J-me})(F),Object.assign(d,{attribution:F})};H("long-animation-frame",d=>{r=r.concat(d),u()}),((d,g={})=>{globalThis.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&le(()=>{Lt();let f,h=P("INP"),T=$(g,ce),y=A=>{Pe(()=>{for(let F of A)T.u(F);let D=T.M();D&&D.T!==h.value&&(h.value=D.T,h.entries=D.entries,f())})},S=H("event",y,{durationThreshold:g.durationThreshold??40});f=L(d,h,Ze,g.reportAllChanges),S&&(S.observe({type:"first-input",buffered:!0}),document.addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&(y(S.takeRecords()),f(!0))}),j(()=>{T.v(),h=P("INP"),f=L(d,h,Ze,g.reportAllChanges)}))})})(d=>{let g=p(d);t(g)},e)},de=class{m;u(e){var o;(o=this.m)==null||o.call(this,e)}},Xe=[2500,4e3],st=(t,e={})=>{let o=$(e=Object.assign({},e),de),r=new WeakMap;o.m=i=>{if(i.element){let a=(e.generateTarget??we)(i.element);r.set(i,a)}},((i,a={})=>{le(()=>{let n=tt(),s,l=P("LCP"),u=$(a,de),m=p=>{a.reportAllChanges||(p=p.slice(-1));for(let d of p)u.u(d),d.startTime<n.firstHiddenTime&&(l.value=Math.max(d.startTime-X(),0),l.entries=[d],s())},c=H("largest-contentful-paint",m);if(c){s=L(i,l,Xe,a.reportAllChanges);let p=Me(()=>{m(c.takeRecords()),c.disconnect(),s(!0)});for(let d of["keydown","click","visibilitychange"])addEventListener(d,()=>Pe(p),{capture:!0,once:!0});j(d=>{l=P("LCP"),s=L(i,l,Xe,a.reportAllChanges),De(()=>{l.value=performance.now()-d.timeStamp,s(!0)})})}})})(i=>{let a=(n=>{let s={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:n.value};if(n.entries.length){let l=q();if(l){let u=l.activationStart||0,m=n.entries.at(-1),c=m.url&&performance.getEntriesByType("resource").filter(f=>f.name===m.url)[0],p=Math.max(0,l.responseStart-u),d=Math.max(p,c?(c.requestStart||c.startTime)-u:0),g=Math.min(n.value,Math.max(d,c?c.responseEnd-u:0));s={target:r.get(m),timeToFirstByte:p,resourceLoadDelay:d-p,resourceLoadDuration:g-d,elementRenderDelay:n.value-g,navigationEntry:l,lcpEntry:m},m.url&&(s.url=m.url),c&&(s.lcpResourceEntry=c)}}return Object.assign(n,{attribution:s})})(i);t(a)},e)},Ye=[800,1800],ke=t=>{document.prerendering?le(()=>ke(t)):document.readyState!=="complete"?addEventListener("load",()=>ke(t),!0):setTimeout(t)},ct=(t,e={})=>{((o,r={})=>{let i=P("TTFB"),a=L(o,i,Ye,r.reportAllChanges);ke(()=>{let n=q();n&&(i.value=Math.max(n.responseStart-X(),0),i.entries=[n],a(!0),j(()=>{i=P("TTFB",0),a=L(o,i,Ye,r.reportAllChanges),a(!0)}))})})(o=>{let r=(i=>{let a={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(i.entries.length){let n=i.entries[0],s=n.activationStart||0,l=Math.max((n.workerStart||n.fetchStart)-s,0),u=Math.max(n.domainLookupStart-s,0),m=Math.max(n.connectStart-s,0),c=Math.max(n.connectEnd-s,0);a={waitingDuration:l,cacheDuration:u-l,dnsDuration:m-u,connectionDuration:c-m,requestDuration:i.value-c,navigationEntry:n}}return Object.assign(i,{attribution:a})})(o);t(r)},e)};function Pt(t){for(let e in t)if(t[e]!==void 0)return!0;return!1}function B(t){return Pt(t)?t:void 0}function lt(){let t=document.getElementById("main");if(!t)return;let e=new Set;try{let a=n=>e.add(n);st(a),it(a),rt(({value:n,...s})=>{e.add({...s,value:n*1e3})}),at(a),ct(a)}catch{}let o=new Set([...performance.getEntriesByType("mark"),...performance.getEntriesByType("measure")].filter(a=>a.name.startsWith("framer-")));new PerformanceObserver(a=>{a.getEntries().forEach(n=>{n.name.startsWith("framer-")&&o.add(n)})}).observe({entryTypes:["measure","mark"]});let r=t.dataset,i={pageOptimizedAt:r.framerPageOptimizedAt?new Date(r.framerPageOptimizedAt).getTime():null,ssrReleasedAt:r.framerSsrReleasedAt?new Date(r.framerSsrReleasedAt).getTime():null,origin:document.location.origin,pathname:document.location.pathname,search:document.location.search};R(()=>At(i),"load"),R(()=>kt(e,o,i),"lazy")}var dt=!1;function kt(t,e,o){let r=document.getElementById("main");if(!r)return;let i=[];if(dt||(i.push(wt(o,r)),dt=!0),t.size>0&&(i.push(...Mt(t,o)),t.clear()),e.size>0){let a=xt(e);a&&i.push(a),e.clear()}w(i)}function wt({pageOptimizedAt:t,ssrReleasedAt:e,origin:o,pathname:r,search:i},a){var l,u,m,c,p,d;let n=performance.getEntriesByType("navigation")[0],s=document.querySelector("[data-framer-css-ssr-minified]");return new E("published_site_performance",{hydrationDurationMs:null,pageLoadDurationMs:null,domNodes:document.getElementsByTagName("*").length,resourcesCount:performance.getEntriesByType("resource").length,headSize:document.head.innerHTML.length,framerCSSSize:(l=s==null?void 0:s.textContent)==null?void 0:l.length,modulePreloads:document.querySelectorAll(`link[rel="modulepreload"][href^="${b().cdn}"]`).length,hasPageContent:a.dataset["framer-no-content"]===void 0,timeZone:V,pageOptimizedAt:t,ssrReleasedAt:e,devicePixelRatio:window.devicePixelRatio,timeToFirstByteMs:null,navigationTiming:n?{activationStart:n.activationStart,connectEnd:n.connectEnd,connectStart:n.connectStart,criticalCHRestart:n.criticalCHRestart,decodedBodySize:n.decodedBodySize,deliveryType:n.deliveryType,domComplete:n.domComplete,domContentLoadedEventEnd:n.domContentLoadedEventEnd,domContentLoadedEventStart:n.domContentLoadedEventStart,domInteractive:n.domInteractive,domainLookupEnd:n.domainLookupEnd,domainLookupStart:n.domainLookupStart,duration:n.duration,encodedBodySize:n.encodedBodySize,fetchStart:n.fetchStart,firstInterimResponseStart:n.firstInterimResponseStart,loadEventEnd:n.loadEventEnd,loadEventStart:n.loadEventStart,nextHopProtocol:n.nextHopProtocol,redirectCount:n.redirectCount,redirectEnd:n.redirectEnd,redirectStart:n.redirectStart,requestStart:n.requestStart,responseEnd:n.responseEnd,responseStart:n.responseStart,responseStatus:n.responseStatus,secureConnectionStart:n.secureConnectionStart,serverTiming:n.serverTiming?JSON.stringify(n.serverTiming):null,startTime:n.startTime,transferSize:n.transferSize,type:n.type,unloadEventEnd:n.unloadEventEnd,unloadEventStart:n.unloadEventStart,workerStart:n.workerStart}:void 0,connection:B({downlink:(u=navigator.connection)==null?void 0:u.downlink,downlinkMax:(m=navigator.connection)==null?void 0:m.downlinkMax,rtt:(c=navigator.connection)==null?void 0:c.rtt,saveData:(p=navigator.connection)==null?void 0:p.saveData,type:(d=navigator.connection)==null?void 0:d.type}),context:{origin:o,pathname:r,search:i}})}var Dt=0;function Mt(t,{pageOptimizedAt:e,ssrReleasedAt:o,origin:r,pathname:i,search:a}){let n=[];return t.forEach(s=>{t.delete(s);let{name:l,value:u,id:m,attribution:c}=s,p={metric:l,label:m,value:Math.round(u),counter:Dt++,pageOptimizedAt:e,ssrReleasedAt:o,context:{origin:r,pathname:i,search:a},attributionLcp:void 0,attributionCls:void 0,attributionInp:void 0,attributionFcp:void 0,attributionTtfb:void 0};l==="LCP"?p.attributionLcp=B({element:c.target,timeToFirstByte:c.timeToFirstByte,resourceLoadDelay:c.resourceLoadDelay,resourceLoadTime:c.resourceLoadDuration,elementRenderDelay:c.elementRenderDelay,url:c.url}):l==="CLS"?p.attributionCls=B({largestShiftTarget:c.largestShiftTarget,largestShiftTime:c.largestShiftTime,largestShiftValue:c.largestShiftValue,loadState:c.loadState}):l==="INP"?p.attributionInp=B({eventTarget:c.interactionTarget,eventType:c.interactionType,eventTime:c.interactionTime?Math.round(c.interactionTime):void 0,loadState:c.loadState,inputDelay:c.inputDelay,processingDuration:c.processingDuration,presentationDelay:c.presentationDelay,nextPaintTime:c.nextPaintTime,totalScriptDuration:c.totalScriptDuration,totalStyleAndLayoutDuration:c.totalStyleAndLayoutDuration,totalPaintDuration:c.totalPaintDuration,totalUnattributedDuration:c.totalUnattributedDuration,longestScript:JSON.stringify(c.longestScript)}):l==="FCP"?p.attributionFcp=B({timeToFirstByte:c.timeToFirstByte,firstByteToFCP:c.firstByteToFCP,loadState:c.loadState}):l==="TTFB"&&(p.attributionTtfb=B({waitingTime:c.waitingDuration,dnsTime:c.dnsDuration,connectionTime:c.connectionDuration,requestTime:c.requestDuration,cacheDuration:c.cacheDuration})),n.push(new E("published_site_performance_web_vitals",p))}),n}function xt(t){let e=[];if(t.forEach(o=>{t.delete(o);let{name:r,startTime:i,duration:a,detail:n}=o,s={name:r,startTime:i,duration:a,detail:n};e.push(s)}),e.length!==0)return new E("published_site_performance_user_timings",{timings:JSON.stringify(e)})}async function At({origin:t,pathname:e,search:o}){let r=document.getElementById("main");if(!r)return;await _();let i=1/0,a=null,n=null,s=0,l=0,u=0,m=0,c=b().cdn,p=`^${c}[^/]+/`,d=".[^.]+.mjs$",g=new RegExp(`${p}script_main${d}`),f=new RegExp(`${p}framer${d}`),h=new RegExp(`${p}motion${d}`),T=performance.getEntriesByType("resource"),y=T.length;for(let A=0;A<y;A++){let D=T[A],{deliveryType:F,initiatorType:v,transferSize:C,decodedBodySize:z,encodedBodySize:Y,name:M,startTime:O}=D;O>i||!(v==="script"&&M.startsWith(c))||(++u,(F==="cache"||F!==void 0&&C===0)&&++m,s+=Y,l+=z,g.test(M)?i=O:a===null&&f.test(M)?a=z:n===null&&h.test(M)&&(n=z))}await _();let S=performance.getEntriesByType("navigation")[0];w([new E("published_site_performance_load",{pageLoadDurationMs:(S==null?void 0:S.domContentLoadedEventEnd)!==void 0&&S.domContentLoadedEventStart!==void 0?Math.round(S.domContentLoadedEventEnd-S.domContentLoadedEventStart):null,resourcesCount:y,domNodes:document.getElementsByTagName("*").length,headSize:document.head.innerHTML.length,headDomNodes:document.head.getElementsByTagName("*").length,bodySize:document.body.innerHTML.length,bodyDomNodes:document.body.getElementsByTagName("*").length,reactRootSize:r.innerHTML.length,reactRootDomNodes:r.getElementsByTagName("*").length,jsSizeDecoded:l,jsSizeEncoded:s,jsCountCached:m,jsCountTotal:u,mainScriptStartTime:Number.isFinite(i)?i:null,libraryJSSizeDecoded:a,motionJSSizeDecoded:n,context:{origin:t,pathname:e,search:o}})])}function ut(){window.__send_framer_event=(t,e)=>{let o=new E(t,e);w([o],"eager")}}var Ft=b(),zt=async()=>{if(await _(!0),Re(),Ft.trackNavigation){$e();let t=typeof document.referrer=="string";Z({initialReferrer:t&&document.referrer||null})}lt(),ut(),Ue()};zt();})();
